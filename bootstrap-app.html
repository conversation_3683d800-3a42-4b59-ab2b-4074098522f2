<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Column Mapping Tool - Bootstrap</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-4 text-primary mb-2">
                <i class="bi bi-diagram-3"></i> Column Mapping Tool
            </h1>
            <p class="lead text-muted">Import CSV files and map columns</p>
        </div>
        
        <!-- Main Content -->
        <div class="row g-4">
            <!-- Left Panel -->
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-arrow-up"></i> Source Columns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="mb-2">
                                <input type="file" id="leftFile" class="form-control" accept=".csv">
                            </div>
                            <small id="leftFileName" class="text-muted fst-italic">No file selected</small>
                        </div>
                        <div id="leftItems" class="border rounded p-3" style="min-height: 300px; max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted fst-italic py-5">
                                <i class="bi bi-upload fs-1 d-block mb-2"></i>
                                Import a CSV file to see columns
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel -->
            <div class="col-md-6">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-arrow-down"></i> Target Columns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="mb-2">
                                <input type="file" id="rightFile" class="form-control" accept=".csv">
                            </div>
                            <small id="rightFileName" class="text-muted fst-italic">No file selected</small>
                        </div>
                        <div id="rightItems" class="border rounded p-3" style="min-height: 300px; max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted fst-italic py-5">
                                <i class="bi bi-upload fs-1 d-block mb-2"></i>
                                Import a CSV file to see columns
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="text-center mt-4">
            <div class="btn-group" role="group">
                <button id="clearAllBtn" class="btn btn-outline-danger">
                    <i class="bi bi-trash"></i> Clear All
                </button>
                <button id="saveBtn" class="btn btn-outline-primary">
                    <i class="bi bi-save"></i> Save
                </button>
                <button id="loadBtn" class="btn btn-outline-secondary">
                    <i class="bi bi-folder-open"></i> Load
                </button>
            </div>
        </div>
        
        <!-- Status -->
        <div class="mt-4">
            <div class="alert alert-info text-center" role="alert">
                <i class="bi bi-info-circle"></i>
                <span id="status">Ready to import CSV files</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log('Bootstrap app loaded');
        
        let leftItems = [];
        let rightItems = [];
        
        // Simple CSV parser
        function parseCSV(csvContent) {
            const lines = csvContent.trim().split('\n');
            if (lines.length === 0) return [];
            
            const firstLine = lines[0];
            let columns = [];
            
            if (firstLine.includes(',')) {
                columns = firstLine.split(',');
            } else if (firstLine.includes('|')) {
                columns = firstLine.split('|');
            } else if (firstLine.includes('\t')) {
                columns = firstLine.split('\t');
            } else {
                columns = firstLine.split(',');
            }
            
            return columns.map(col => col.trim().replace(/^["']|["']$/g, ''));
        }
        
        // Render items with Bootstrap styling
        function renderItems(containerId, items) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            if (items.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted fst-italic py-5">
                        <i class="bi bi-upload fs-1 d-block mb-2"></i>
                        Import a CSV file to see columns
                    </div>
                `;
                return;
            }
            
            items.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'list-group-item list-group-item-action border-0 mb-1 rounded';
                itemElement.style.cursor = 'pointer';
                itemElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-circle me-2 text-muted"></i>
                        <span>${item}</span>
                    </div>
                `;
                container.appendChild(itemElement);
            });
        }
        
        // Handle file import
        function handleFileImport(event, side) {
            console.log(`File import for ${side} side`);
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const csvContent = e.target.result;
                const columns = parseCSV(csvContent);
                
                if (side === 'left') {
                    leftItems = columns;
                    document.getElementById('leftFileName').textContent = file.name;
                    renderItems('leftItems', columns);
                } else {
                    rightItems = columns;
                    document.getElementById('rightFileName').textContent = file.name;
                    renderItems('rightItems', columns);
                }
                
                document.getElementById('status').textContent = 
                    `Imported ${columns.length} columns from ${file.name}`;
                
                console.log(`Imported ${columns.length} columns:`, columns);
            };
            
            reader.readAsText(file);
        }
        
        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up Bootstrap app');
            
            const leftFile = document.getElementById('leftFile');
            const rightFile = document.getElementById('rightFile');
            
            if (leftFile) {
                leftFile.addEventListener('click', () => console.log('Left file clicked'));
                leftFile.addEventListener('change', (e) => handleFileImport(e, 'left'));
            }
            
            if (rightFile) {
                rightFile.addEventListener('click', () => console.log('Right file clicked'));
                rightFile.addEventListener('change', (e) => handleFileImport(e, 'right'));
            }
            
            // Control buttons
            document.getElementById('clearAllBtn')?.addEventListener('click', () => {
                leftItems = [];
                rightItems = [];
                renderItems('leftItems', []);
                renderItems('rightItems', []);
                document.getElementById('leftFileName').textContent = 'No file selected';
                document.getElementById('rightFileName').textContent = 'No file selected';
                document.getElementById('status').textContent = 'All data cleared';
            });
            
            console.log('Bootstrap app initialized');
        });
    </script>
</body>
</html>
