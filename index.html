<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>Drag & Drop Column Mappings</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <header>
            <h1>Column Mapping Tool</h1>
            <p>Import CSV files and draw connections between columns</p>
        </header>
        
        <div class="main-content">
            <!-- Left Panel -->
            <div class="panel left-panel">
                <div class="panel-header">
                    <h2>Source Columns</h2>
                    <div class="import-section">
                        <input type="file" id="leftFileInput" class="file-input" accept=".csv">
                        <span id="leftFileName" class="file-name">No file selected</span>
                    </div>
                </div>
                <div class="items-container" id="leftItems">
                    <div class="placeholder">Import a CSV file to see columns</div>
                </div>
            </div>
            
            <!-- Canvas for drawing lines -->
            <div class="canvas-container">
                <canvas id="connectionCanvas"></canvas>
            </div>
            
            <!-- Right Panel -->
            <div class="panel right-panel">
                <div class="panel-header">
                    <h2>Target Columns</h2>
                    <div class="import-section">
                        <input type="file" id="rightFileInput" class="file-input" accept=".csv">
                        <span id="rightFileName" class="file-name">No file selected</span>
                    </div>
                </div>
                <div class="items-container" id="rightItems">
                    <div class="placeholder">Import a CSV file to see columns</div>
                </div>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button id="clearAllBtn" class="control-btn">Clear All Connections</button>
            <button id="saveBtn" class="control-btn">Save Mappings</button>
            <button id="loadBtn" class="control-btn">Load Mappings</button>
        </div>
        
        <!-- Status Bar -->
        <div class="status-bar">
            <span id="statusText">Ready to import CSV files</span>
            <span id="connectionCount">Connections: 0</span>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
