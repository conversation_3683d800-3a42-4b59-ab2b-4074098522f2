class ColumnMappingApp {
    constructor() {
        this.leftItems = [];
        this.rightItems = [];
        this.connections = [];
        this.selectedLeftItem = null;
        this.isDrawing = false;
        this.canvas = null;
        this.ctx = null;
        
        this.init();
    }
    
    init() {
        console.log('Initializing ColumnMappingApp');
        this.setupCanvas();
        this.setupEventListeners();
        this.loadFromStorage();
        this.updateStatus('Ready to import CSV files');
        console.log('ColumnMappingApp initialized successfully');
    }
    
    setupCanvas() {
        this.canvas = document.getElementById('connectionCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();
        
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        const container = document.querySelector('.main-content');
        this.canvas.width = container.offsetWidth;
        this.canvas.height = container.offsetHeight;
        this.redrawConnections();
    }
    
    setupEventListeners() {
        // File input listeners using Method 1 (direct HTML file inputs)
        const leftFileInput = document.getElementById('leftFileInput');
        const rightFileInput = document.getElementById('rightFileInput');

        if (leftFileInput) {
            // Add click listener to debug
            leftFileInput.addEventListener('click', (e) => {
                console.log('Left file input clicked - file dialog should open');
            });

            leftFileInput.addEventListener('change', (e) => {
                console.log('Left file input changed', e.target.files);
                if (e.target.files.length > 0) {
                    console.log('File selected:', e.target.files[0].name);
                    this.handleFileImport(e, 'left');
                } else {
                    console.log('No file selected');
                }
            });
        } else {
            console.error('Left file input not found');
        }

        if (rightFileInput) {
            // Add click listener to debug
            rightFileInput.addEventListener('click', (e) => {
                console.log('Right file input clicked - file dialog should open');
            });

            rightFileInput.addEventListener('change', (e) => {
                console.log('Right file input changed', e.target.files);
                if (e.target.files.length > 0) {
                    console.log('File selected:', e.target.files[0].name);
                    this.handleFileImport(e, 'right');
                } else {
                    console.log('No file selected');
                }
            });
        } else {
            console.error('Right file input not found');
        }

        // Control button listeners
        const clearAllBtn = document.getElementById('clearAllBtn');
        const saveBtn = document.getElementById('saveBtn');
        const loadBtn = document.getElementById('loadBtn');

        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                this.clearAllConnections();
            });
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveToStorage();
            });
        }

        if (loadBtn) {
            loadBtn.addEventListener('click', () => {
                this.loadFromStorage();
            });
        }

        // Canvas listeners for line deletion
        if (this.canvas) {
            this.canvas.addEventListener('click', (e) => {
                this.handleCanvasClick(e);
            });
        }
    }

    handleFileImport(event, side) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const csvContent = e.target.result;
            const columns = this.parseCSV(csvContent);
            
            if (side === 'left') {
                this.leftItems = columns;
                document.getElementById('leftFileName').textContent = file.name;
                this.renderItems('leftItems', columns, 'left');
            } else {
                this.rightItems = columns;
                document.getElementById('rightFileName').textContent = file.name;
                this.renderItems('rightItems', columns, 'right');
            }
            
            this.updateStatus(`Imported ${columns.length} columns from ${file.name}`);
        };
        
        reader.readAsText(file);
    }
    
    parseCSV(csvContent) {
        const lines = csvContent.trim().split('\n');
        if (lines.length === 0) return [];
        
        const firstLine = lines[0];
        let columns = [];
        
        // Try different delimiters
        if (firstLine.includes(',')) {
            columns = this.parseCSVLine(firstLine, ',');
        } else if (firstLine.includes('|')) {
            columns = this.parseCSVLine(firstLine, '|');
        } else if (firstLine.includes('\t')) {
            columns = this.parseCSVLine(firstLine, '\t');
        } else {
            // Fallback: split by comma
            columns = firstLine.split(',');
        }
        
        // Clean up column names
        return columns.map(col => col.trim().replace(/^["']|["']$/g, ''));
    }
    
    parseCSVLine(line, delimiter) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"' || char === "'") {
                inQuotes = !inQuotes;
            } else if (char === delimiter && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current);
        return result;
    }
    
    renderItems(containerId, items, side) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        if (items.length === 0) {
            container.innerHTML = '<div class="placeholder">Import a CSV file to see columns</div>';
            return;
        }
        
        items.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'item';
            itemElement.textContent = item;
            itemElement.dataset.index = index;
            itemElement.dataset.side = side;
            
            // Add click listener for connection creation
            itemElement.addEventListener('click', (e) => {
                this.handleItemClick(e, side, index);
            });
            
            container.appendChild(itemElement);
        });
    }
    
    handleItemClick(event, side, index) {
        const itemElement = event.target;
        
        if (side === 'left') {
            // Select left item for connection
            this.clearSelection();
            this.selectedLeftItem = { element: itemElement, index: index };
            itemElement.classList.add('selected');
            this.updateStatus('Selected left item. Click a right item to create connection.');
        } else if (side === 'right' && this.selectedLeftItem) {
            // Create connection
            this.createConnection(this.selectedLeftItem.index, index);
            this.clearSelection();
            this.updateStatus('Connection created!');
        }
    }
    
    createConnection(leftIndex, rightIndex) {
        // Check if connection already exists
        const existingConnection = this.connections.find(conn => 
            conn.left === leftIndex && conn.right === rightIndex
        );
        
        if (existingConnection) {
            this.updateStatus('Connection already exists!');
            return;
        }
        
        this.connections.push({
            left: leftIndex,
            right: rightIndex,
            id: Date.now() + Math.random()
        });
        
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
    }
    
    clearSelection() {
        document.querySelectorAll('.item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        this.selectedLeftItem = null;
    }
    
    updateConnectedItems() {
        // Update visual indicators for connected items
        document.querySelectorAll('.item').forEach(item => {
            item.classList.remove('connected');
        });
        
        this.connections.forEach(conn => {
            const leftItem = document.querySelector(`[data-side="left"][data-index="${conn.left}"]`);
            const rightItem = document.querySelector(`[data-side="right"][data-index="${conn.right}"]`);
            
            if (leftItem) leftItem.classList.add('connected');
            if (rightItem) rightItem.classList.add('connected');
        });
    }
    
    redrawConnections() {
        if (!this.ctx) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.connections.forEach(conn => {
            this.drawConnection(conn);
        });
    }
    
    drawConnection(connection) {
        const leftItem = document.querySelector(`[data-side="left"][data-index="${connection.left}"]`);
        const rightItem = document.querySelector(`[data-side="right"][data-index="${connection.right}"]`);

        if (!leftItem || !rightItem) return;

        const leftRect = leftItem.getBoundingClientRect();
        const rightRect = rightItem.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        const startX = leftRect.right - canvasRect.left;
        const startY = leftRect.top + leftRect.height / 2 - canvasRect.top;
        const endX = rightRect.left - canvasRect.left;
        const endY = rightRect.top + rightRect.height / 2 - canvasRect.top;

        this.ctx.beginPath();
        this.ctx.moveTo(startX, startY);
        this.ctx.lineTo(endX, endY);
        this.ctx.strokeStyle = '#4caf50';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // Draw connection points
        this.ctx.beginPath();
        this.ctx.arc(startX, startY, 4, 0, 2 * Math.PI);
        this.ctx.fillStyle = '#4caf50';
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.arc(endX, endY, 4, 0, 2 * Math.PI);
        this.ctx.fill();
    }

    handleCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Check if click is near any connection line
        for (let i = 0; i < this.connections.length; i++) {
            const conn = this.connections[i];
            if (this.isClickNearConnection(x, y, conn)) {
                this.deleteConnection(i);
                this.updateStatus('Connection deleted!');
                break;
            }
        }
    }

    isClickNearConnection(clickX, clickY, connection) {
        const leftItem = document.querySelector(`[data-side="left"][data-index="${connection.left}"]`);
        const rightItem = document.querySelector(`[data-side="right"][data-index="${connection.right}"]`);

        if (!leftItem || !rightItem) return false;

        const leftRect = leftItem.getBoundingClientRect();
        const rightRect = rightItem.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        const startX = leftRect.right - canvasRect.left;
        const startY = leftRect.top + leftRect.height / 2 - canvasRect.top;
        const endX = rightRect.left - canvasRect.left;
        const endY = rightRect.top + rightRect.height / 2 - canvasRect.top;

        // Calculate distance from click point to line
        const distance = this.distanceToLine(clickX, clickY, startX, startY, endX, endY);
        return distance < 10; // 10 pixel tolerance
    }

    distanceToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        const param = dot / lenSq;

        let xx, yy;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }

    deleteConnection(index) {
        this.connections.splice(index, 1);
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
    }

    clearAllConnections() {
        this.connections = [];
        this.clearSelection();
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
        this.updateStatus('All connections cleared!');
    }

    updateConnectionCount() {
        document.getElementById('connectionCount').textContent = `Connections: ${this.connections.length}`;
    }

    updateStatus(message) {
        document.getElementById('statusText').textContent = message;
    }

    saveToStorage() {
        const data = {
            leftItems: this.leftItems,
            rightItems: this.rightItems,
            connections: this.connections,
            leftFileName: document.getElementById('leftFileName').textContent,
            rightFileName: document.getElementById('rightFileName').textContent
        };

        localStorage.setItem('columnMappings', JSON.stringify(data));
        this.updateStatus('Mappings saved to browser storage!');
    }

    loadFromStorage() {
        const saved = localStorage.getItem('columnMappings');
        if (!saved) {
            this.updateStatus('No saved mappings found');
            return;
        }

        try {
            const data = JSON.parse(saved);

            this.leftItems = data.leftItems || [];
            this.rightItems = data.rightItems || [];
            this.connections = data.connections || [];

            if (data.leftFileName) {
                document.getElementById('leftFileName').textContent = data.leftFileName;
            }
            if (data.rightFileName) {
                document.getElementById('rightFileName').textContent = data.rightFileName;
            }

            this.renderItems('leftItems', this.leftItems, 'left');
            this.renderItems('rightItems', this.rightItems, 'right');
            this.updateConnectedItems();
            this.redrawConnections();
            this.updateConnectionCount();

            this.updateStatus('Mappings loaded from storage!');
        } catch (error) {
            this.updateStatus('Error loading saved mappings');
            console.error('Error loading from storage:', error);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded');
    // Add a small delay to ensure all elements are ready
    setTimeout(() => {
        console.log('Creating ColumnMappingApp instance');
        new ColumnMappingApp();
    }, 100);
});
